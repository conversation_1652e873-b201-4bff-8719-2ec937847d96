import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiExcludeEndpoint, ApiTags } from '@nestjs/swagger';
import { ApplicationService } from './application.service';
import {
  CreateAppDto,
  DeleteAppDto,
  FetchAppsDto,
  FetchSingleAppDto,
  UpdateAppDto,
  WhitelistValidatorDto,
} from './dto/application.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '../user/entities/user.entity';
import { Request } from 'express';
import { OriginType } from 'src/origins/entities/origins.entity';
@ApiTags('Application')
@Controller('users/app')
export class ApplicationController {
  constructor(private readonly applicationService: ApplicationService) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('create-app')
  async createApp(
    @Req() req: { user: User },
    @Body() createAppDto: CreateAppDto,
  ) {
    const userId = req.user.id;
    return await this.applicationService.createApp(userId, createAppDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-app')
  async updateApp(
    @Req() req: { user: User },
    @Body() updateAppDto: UpdateAppDto,
  ) {
    const userId = req.user.id;
    return await this.applicationService.updateApp(userId, updateAppDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-single-app')
  async getSingleApp(
    @Req() req: { user: User },
    @Query() query: FetchSingleAppDto,
  ) {
    const userId = req.user.id;
    return await this.applicationService.getSingleApp(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-all-apps')
  async getAllApp(@Req() req: { user: User }, @Query() query: FetchAppsDto) {
    const userId = req.user.id;
    return await this.applicationService.getAllApps(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('delete-app')
  async deleteApiKey(
    @Req() req: { user: User },
    @Query() payload: DeleteAppDto,
  ) {
    const userId = req.user.id;
    return this.applicationService.deleteApp(userId, payload);
  }

  @ApiExcludeEndpoint()
  @Post('key-validator')
  async getkeyValidated(
    @Req() req: Request,
    @Body() whitelistValidatorDto: WhitelistValidatorDto,
  ) {
    const reqOrigin = req.headers.origin;
    if (
      ![
        process.env.PAYMASTER_ORIGIN,
        process.env.BUNDLER_ORIGIN,
        process.env.RELAYER_ORIGIN,
        process.env.CHATAI_ORIGIN,
      ].includes(reqOrigin)
    ) {
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'You are not allowed to use this api',
      };
    }
    const chainId = whitelistValidatorDto.chainId;
    const apiKey = whitelistValidatorDto.apikey;
    const origin = whitelistValidatorDto.origin;
    const payload: any = whitelistValidatorDto.payload;
    const type = whitelistValidatorDto.type;
    const name = whitelistValidatorDto.name;
    if (type != OriginType.BUNDLER && !payload.params) {
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Payload not valid',
      };
    }

    return await this.applicationService.apiKeyValidator(
      chainId,
      apiKey,
      origin,
      payload,
      type,
      name,
    );
  }
}
